
"use client"

import * as React from "react"
import { Chevron<PERSON>eft, ChevronRight } from "lucide-react"
import { DayPicker, DropdownProps, useDayPicker as useReactDayPicker } from "react-day-picker"
import HijriDate from "hijri-date"
import { ar } from "date-fns/locale"

import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./select"
import { ScrollArea } from "./scroll-area"

// --- Hijri Helpers ---
const hijriMonths = [
  "محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأولى", "جمادى الآخرة",
  "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
];

function HijriDropdown(props: DropdownProps) {
  const { fromDate, toDate, fromMonth, toMonth } = useReactDayPicker();
  
  const currentHijriYear = new HijriDate().getFullYear();
  const fromYear = fromMonth ? new HijriDate(fromMonth).getFullYear() : currentHijriYear - 100;
  const toYear = toMonth ? new HijriDate(toMonth).getFullYear() : currentHijriYear + 10;

  const handleMonthChange = (monthIndex: number) => {
    const currentMonth = props.value instanceof Date ? new HijriDate(props.value) : new HijriDate();
    const newMonth = new HijriDate(currentMonth.getFullYear(), parseInt(monthIndex.toString(), 10) + 1, 1).toGregorian();
    props.onChange?.(newMonth);
  };

  const handleYearChange = (year: number) => {
    const currentMonth = props.value instanceof Date ? new HijriDate(props.value) : new HijriDate();
    const newMonth = new HijriDate(year, currentMonth.getMonth(), 1).toGregorian();
    props.onChange?.(newMonth);
  };

  const selectedHijri = props.value instanceof Date ? new HijriDate(props.value) : new HijriDate();
  
  if (props.name === 'months') {
    return (
      <Select onValueChange={(value) => handleMonthChange(parseInt(value))} value={String(selectedHijri.getMonth() - 1)}>
        <SelectTrigger className="w-fit h-auto p-1 text-xs">
          <SelectValue>{hijriMonths[selectedHijri.getMonth() - 1]}</SelectValue>
        </SelectTrigger>
        <SelectContent>
          <ScrollArea className="h-64">
            {hijriMonths.map((month, i) => (
              <SelectItem key={i} value={String(i)}>{month}</SelectItem>
            ))}
          </ScrollArea>
        </SelectContent>
      </Select>
    );
  }

  if (props.name === 'years') {
    const years = Array.from({ length: toYear - fromYear + 1 }, (_, i) => fromYear + i);
    return (
      <Select onValueChange={(value) => handleYearChange(parseInt(value))} value={String(selectedHijri.getFullYear())}>
        <SelectTrigger className="w-fit h-auto p-1 text-xs">
          <SelectValue>{selectedHijri.getFullYear()}</SelectValue>
        </SelectTrigger>
        <SelectContent>
          <ScrollArea className="h-64">
            {years.map((year) => (
              <SelectItem key={year} value={String(year)}>{year}</SelectItem>
            ))}
          </ScrollArea>
        </SelectContent>
      </Select>
    );
  }
  return null;
}

function GregorianDropdown(props: DropdownProps) {
  const { fromYear, toYear } = useReactDayPicker();
  const options = [];
  const dateValue = props.value instanceof Date ? props.value : new Date();

  if (props.name === 'months') {
    for (let i = 0; i < 12; i++) {
        const d = new Date(2000, i);
        options.push({ value: i, label: d.toLocaleDateString('ar-SA', { month: 'long' }) });
    }
  } else if (props.name === 'years') {
    const startYear = fromYear || new Date().getFullYear() - 100;
    const endYear = toYear || new Date().getFullYear();
    for (let i = startYear; i <= endYear; i++) {
      options.push({ value: i, label: i.toString() });
    }
  }

  const selectedValue = props.name === 'months' ? dateValue.getMonth() : dateValue.getFullYear();

  return (
    <Select
      value={String(selectedValue)}
      onValueChange={(value) => {
        const newDate = new Date(dateValue);
        if (props.name === 'months') {
          newDate.setMonth(parseInt(value, 10));
        } else if (props.name === 'years') {
          newDate.setFullYear(parseInt(value, 10));
        }
        props.onChange?.(newDate);
      }}
    >
      <SelectTrigger className="w-fit h-auto p-1 text-xs">
        <SelectValue>
          {props.name === 'months' 
            ? dateValue.toLocaleDateString('ar-SA', { month: 'long'}) 
            : dateValue.getFullYear()}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        <ScrollArea className="h-64">
          {options.map((option) => (
            <SelectItem key={option.value} value={String(option.value)}>
              {option.label}
            </SelectItem>
          ))}
        </ScrollArea>
      </SelectContent>
    </Select>
  )
}

// --- Main Calendar Component ---
export type CalendarProps = React.ComponentProps<typeof DayPicker> & { calendarType?: 'gregorian' | 'hijri' };

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  calendarType = 'gregorian',
  ...props
}: CalendarProps) {
  const isHijri = calendarType === 'hijri';

  const formatters = isHijri ? {
    formatCaption: (date: Date) => {
      const hijri = new HijriDate(date);
      return `${hijriMonths[hijri.getMonth() - 1]} ${hijri.getFullYear()}`;
    },
    formatDay: (date: Date) => new HijriDate(date).getDate().toString(),
    formatWeekday: (date: Date) => new Intl.DateTimeFormat('ar-SA', { weekday: 'short' }).format(date),
  } : {
    formatWeekday: (date: Date) => new Intl.DateTimeFormat('ar-SA', { weekday: 'short' }).format(date),
  };
  
  const currentHijriYear = new HijriDate().getFullYear();

  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn("p-3", className)}
      classNames={{
        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
        month: "space-y-4",
        caption: "flex justify-center pt-1 relative items-center",
        caption_label: "text-sm font-medium hidden",
        caption_dropdowns: "flex justify-center gap-2",
        nav: "space-x-1 flex items-center",
        nav_button: cn(
          buttonVariants({ variant: "outline" }),
          "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
        ),
        nav_button_previous: "absolute right-1",
        nav_button_next: "absolute left-1",
        table: "w-full border-collapse space-y-1",
        head_row: "flex",
        head_cell: "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
        row: "flex w-full mt-2",
        cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
        day: cn(
          buttonVariants({ variant: "ghost" }),
          "h-9 w-9 p-0 font-normal aria-selected:opacity-100"
        ),
        day_range_end: "day-range-end",
        day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
        day_today: "bg-accent text-accent-foreground",
        day_outside: "day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground",
        day_disabled: "text-muted-foreground opacity-50",
        day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
        day_hidden: "invisible",
        ...classNames,
      }}
      components={{
        IconLeft: ({ ...props }) => <ChevronRight className="h-4 w-4" />,
        IconRight: ({ ...props }) => <ChevronLeft className="h-4 w-4" />,
        Dropdown: isHijri ? HijriDropdown : GregorianDropdown,
      }}
      captionLayout="dropdown-buttons"
      fromYear={isHijri ? currentHijriYear - 100 : new Date().getFullYear() - 100}
      toYear={isHijri ? currentHijriYear + 10 : new Date().getFullYear() + 10}
      formatters={formatters}
      locale={ar}
      dir="rtl"
      {...props}
    />
  )
}
Calendar.displayName = "Calendar"

export { Calendar }
