
'use client';

import { useState } from 'react';
import { useForm, Control } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { differenceInYears, subYears, differenceInMonths, subMonths, differenceInDays } from 'date-fns';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  day: requiredNumber('اليوم مطلوب').int().min(1, 'يوم غير صالح').max(31, 'يوم غير صالح'),
  month: requiredNumber('الشهر مطلوب').int().min(1, 'شهر غير صالح').max(12, 'شهر غير صالح'),
  year: requiredNumber('السنة مطلوبة').int().min(1900, 'سنة غير صالحة').max(new Date().getFullYear(), 'سنة غير صالحة'),
}).refine(data => {
    try {
        const date = new Date(data.year, data.month - 1, data.day);
        return date.getFullYear() === data.year && date.getMonth() === data.month - 1 && date.getDate() === data.day;
    } catch {
        return false;
    }
}, {
    message: 'التاريخ المدخل غير صالح.',
    path: ['day'],
});

type FormValues = z.infer<typeof FormSchema>;

interface Result {
  years: number;
  months: number;
  days: number;
}

const DateFields = ({ control }: { control: Control<FormValues> }) => (
    <div className="grid grid-cols-3 gap-4">
        <FormField
            control={control}
            name="day"
            render={({ field }) => (
                <FormItem>
                    <FormLabel>اليوم</FormLabel>
                    <FormControl>
                        <Input type="number" placeholder="مثال: 23" min="1" max="31" {...field} />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
        <FormField
            control={control}
            name="month"
            render={({ field }) => (
                <FormItem>
                    <FormLabel>الشهر</FormLabel>
                    <FormControl>
                        <Input type="number" placeholder="مثال: 9" min="1" max="12" {...field} />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
        <FormField
            control={control}
            name="year"
            render={({ field }) => (
                <FormItem>
                    <FormLabel>السنة</FormLabel>
                    <FormControl>
                        <Input type="number" placeholder="مثال: 1990" min="1" {...field} />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
    </div>
);


export function AgeCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      day: undefined,
      month: undefined,
      year: undefined,
    }
  });

  function onSubmit(data: FormValues) {
    const today = new Date();
    const birthDate = new Date(data.year, data.month - 1, data.day);

    if (birthDate > today) {
      form.setError('year', { message: 'تاريخ الميلاد لا يمكن أن يكون في المستقبل' });
      return;
    }

    const years = differenceInYears(today, birthDate);
    const pastYearDate = subYears(today, years);
    const months = differenceInMonths(pastYearDate, birthDate);
    const pastMonthDate = subMonths(pastYearDate, months);
    const days = differenceInDays(pastMonthDate, birthDate);

    setResult({ years, months, days });
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>حساب العمر</CardTitle>
        <CardDescription>أدخل تاريخ ميلادك لحساب عمرك بدقة.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <DateFields control={form.control} />
            <Button type="submit" className="w-full">
              احسب العمر
            </Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-4 text-center">عمرك هو</h3>
            <div className="grid grid-cols-3 gap-4">
              <div className="p-4 bg-primary/10 rounded-lg">
                <p className="text-4xl font-bold font-mono text-primary">{result.years}</p>
                <p className="text-sm text-muted-foreground">سنوات</p>
              </div>
              <div className="p-4 bg-primary/10 rounded-lg">
                <p className="text-4xl font-bold font-mono text-primary">{result.months}</p>
                <p className="text-sm text-muted-foreground">أشهر</p>
              </div>
              <div className="p-4 bg-primary/10 rounded-lg">
                <p className="text-4xl font-bold font-mono text-primary">{result.days}</p>
                <p className="text-sm text-muted-foreground">أيام</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
