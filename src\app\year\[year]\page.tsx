

'use client';

import { useParams } from 'next/navigation';
import HijriDate from 'hijri-date';
import { differenceInYears, subYears, differenceInMonths, subMonths, differenceInDays, format } from 'date-fns';
import { PageHeader } from '@/components/PageHeader';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import React, { useState, useEffect } from 'react';
import { useForm, Control } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { ar } from 'date-fns/locale';

interface PageData {
    pageTitle: string;
    year: number;
    isHijri: boolean;
    gregorianYear: number;
    hijriYear: number;
    currentYear: number;
    approximateAge: {
        lower: number;
        upper: number;
    };
    currentDateInBirthYear: string;
}

// --- PRECISE AGE CALCULATOR COMPONENT ---
const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const hijriMonths = [
  "محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأولى", "جمادى الآخرة", 
  "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
];

const gregorianMonths = [
  "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", 
  "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
];

interface PreciseAgeCalculatorProps {
  year: number;
  isHijri: boolean;
}

interface PreciseAgeResult {
  years: number;
  months: number;
  days: number;
}

function PreciseAgeCalculator({ year, isHijri }: PreciseAgeCalculatorProps) {
  const [result, setResult] = useState<PreciseAgeResult | null>(null);

  const FormSchema = z.object({
    day: requiredNumber('اليوم مطلوب').int().min(1).max(31),
    month: requiredNumber('الشهر مطلوب').int().min(1).max(12),
  }).refine(data => {
      try {
          if (isHijri) {
              new HijriDate(year, data.month, data.day).toGregorian();
              return true;
          } else {
              const date = new Date(year, data.month - 1, data.day);
              return date.getFullYear() === year && date.getMonth() === data.month - 1 && date.getDate() === data.day;
          }
      } catch {
          return false;
      }
  }, {
      message: 'التاريخ المدخل غير صالح.',
      path: ['day'],
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const today = new Date();
    let birthDate: Date;

    if (isHijri) {
      birthDate = new HijriDate(year, data.month, data.day).toGregorian();
    } else {
      birthDate = new Date(year, data.month - 1, data.day);
    }

    if (birthDate > today) {
      form.setError('day', { message: 'تاريخ الميلاد لا يمكن أن يكون في المستقبل' });
      return;
    }

    const years = differenceInYears(today, birthDate);
    const pastYearDate = subYears(today, years);
    const months = differenceInMonths(pastYearDate, birthDate);
    const pastMonthDate = subMonths(pastYearDate, months);
    const days = differenceInDays(pastMonthDate, birthDate);

    setResult({ years, months, days });
  }

  const months = isHijri ? hijriMonths : gregorianMonths;

  return (
    <Card className="mt-12">
      <CardHeader>
        <CardTitle>حساب العمر بدقة</CardTitle>
        <CardContent className="pt-6">
          <p className="mb-4 text-muted-foreground">أدخل يوم وشهر ميلادك للحصول على عمرك الدقيق بالسنوات والأشهر والأيام.</p>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="day"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                           <Input type="number" placeholder="اليوم" min="1" max="31" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="month"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                          <Input type="number" placeholder="الشهر" min="1" max="12" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Input value={year} readOnly disabled className="text-center" />
              </div>
              <Button type="submit" className="w-full">احسب العمر بدقة</Button>
            </form>
          </Form>
          {result && (
            <div className="mt-8 text-center">
              <h3 className="text-xl font-headline font-semibold mb-4 text-center">عمرك الدقيق هو</h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="p-4 bg-primary/10 rounded-lg">
                  <p className="text-4xl font-bold font-mono text-primary">{result.years}</p>
                  <p className="text-sm text-muted-foreground">سنوات</p>
                </div>
                <div className="p-4 bg-primary/10 rounded-lg">
                  <p className="text-4xl font-bold font-mono text-primary">{result.months}</p>
                  <p className="text-sm text-muted-foreground">أشهر</p>
                </div>
                <div className="p-4 bg-primary/10 rounded-lg">
                  <p className="text-4xl font-bold font-mono text-primary">{result.days}</p>
                  <p className="text-sm text-muted-foreground">أيام</p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </CardHeader>
    </Card>
  );
}

// --- MAIN PAGE COMPONENT ---
export default function YearPage() {
    const params = useParams<{ year: string }>();
    const yearParam = params?.year;
    const [pageData, setPageData] = useState<PageData | null>(null);

    useEffect(() => {
        if (!yearParam) return;

        async function fetchData() {
            const isHijri = /[هhH]$/.test(yearParam);
            const yearNum = parseInt(yearParam.replace(/[هhH]/, ''));
            
            if (isNaN(yearNum) || yearNum < 1) {
                setPageData(null); 
                return;
            }
            if (!isHijri && yearNum > new Date().getFullYear() + 5) {
                 setPageData(null); 
                return;
            }

            let hijriYear: number;
            let gregorianYear: number;
            let pageTitle: string;
            let approxAgeLower: number;
            let approxAgeUpper: number;
            let currentYear: number;
            let currentDateInBirthYear: string;


            try {
                if (isHijri) {
                    const hijri = new HijriDate(yearNum, 1, 1);
                    const gregorianDate = hijri.toGregorian();
                    gregorianYear = gregorianDate.getFullYear();
                    hijriYear = yearNum;
                    
                    const currentHijri = new HijriDate();
                    currentYear = currentHijri.getFullYear();
                    pageTitle = `مواليد ${yearNum}هـ كم عمرهم في ${currentYear}هـ؟`;
                    approxAgeUpper = currentYear - hijriYear;
                    currentDateInBirthYear = `${yearNum}/${currentHijri.getMonth()}/${currentHijri.getDate()}`;

                } else {
                    const hijri = new Date(yearNum, 0, 1).toHijri();
                    hijriYear = hijri.getFullYear();
                    gregorianYear = yearNum;

                    currentYear = new Date().getFullYear();
                    pageTitle = `مواليد ${yearNum} كم عمرهم في ${currentYear}؟`;
                    approxAgeUpper = currentYear - gregorianYear;
                    const today = new Date();
                    currentDateInBirthYear = `${yearNum}/${today.getMonth() + 1}/${today.getDate()}`;
                }
                approxAgeLower = approxAgeUpper - 1;

            } catch (e) {
                setPageData(null); 
                return;
            }


            setPageData({
                pageTitle,
                year: yearNum,
                isHijri,
                gregorianYear,
                hijriYear,
                currentYear,
                approximateAge: { lower: approxAgeLower, upper: approxAgeUpper },
                currentDateInBirthYear,
            });
        }

        fetchData();
    }, [yearParam]);

    if (!pageData) {
      return null;
    }

    const { pageTitle, year, isHijri, gregorianYear, hijriYear, approximateAge, currentDateInBirthYear, currentYear } = pageData;

    return (
        <div className="w-full max-w-4xl mx-auto p-4 sm:p-6 md:p-8">
            <PageHeader title={pageTitle} description={`كل ما تريد معرفته عن مواليد عام ${year}${isHijri ? 'هـ' : ''} وأعمارهم.`} />
            
            <div className="space-y-8">
                <Card>
                    <CardHeader>
                        <CardTitle>العمر الحالي (تقريبي)</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6 text-center">
                        <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                             <p className="text-lg">عمر مواليد عام <strong>{year}{isHijri ? 'هـ' : ''}</strong> الذين ولدوا في أو قبل تاريخ <strong>{currentDateInBirthYear}</strong> هو</p>
                             <p className="text-5xl font-bold font-mono text-green-700 my-2">{approximateAge.upper}</p>
                             <p className="text-lg">سنة</p>
                        </div>
                        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                             <p className="text-lg">عمر مواليد عام <strong>{year}{isHijri ? 'هـ' : ''}</strong> الذين ولدوا بعد تاريخ <strong>{currentDateInBirthYear}</strong> هو</p>
                             <p className="text-5xl font-bold font-mono text-blue-700 my-2">{approximateAge.lower}</p>
                             <p className="text-lg">سنة</p>
                        </div>
                         <p className="text-sm mt-4 text-muted-foreground">تمت الإجابة على عمر مواليد عام {year}{isHijri ? 'هـ' : ''} بدقة وفقاً لما يوافق تاريخ اليوم.</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>التقويم</CardTitle>
                    </CardHeader>
                    <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="text-center p-4 bg-secondary rounded-lg">
                            <p className="text-sm text-muted-foreground">السنة الميلادية الموافقة</p>
                            <p className="text-2xl font-semibold">{gregorianYear} - {gregorianYear + (isHijri ? 1 : 0)}</p>
                        </div>
                        <div className="text-center p-4 bg-secondary rounded-lg">
                            <p className="text-sm text-muted-foreground">السنة الهجرية الموافقة</p>
                            <p className="text-2xl font-semibold">{hijriYear}{!isHijri && ` - ${hijriYear+1}`}هـ</p>
                        </div>
                    </CardContent>
                </Card>
            </div>
            
            <PreciseAgeCalculator year={year} isHijri={isHijri} />

        </div>
    );
}
