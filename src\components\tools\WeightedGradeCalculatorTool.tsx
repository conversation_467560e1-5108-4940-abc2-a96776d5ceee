
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useState } from 'react';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const formSchema = z.object({
  highSchoolGrade: requiredNumber().min(0, "الدرجة لا يمكن أن تكون سالبة.").max(100, "الدرجة لا يمكن أن تزيد عن 100."),
  qudratGrade: requiredNumber().min(0, "الدرجة لا يمكن أن تكون سالبة.").max(100, "الدرجة لا يمكن أن تزيد عن 100."),
  tahsiliGrade: requiredNumber().min(0, "الدرجة لا يمكن أن تكون سالبة.").max(100, "الدرجة لا يمكن أن تزيد عن 100."),
});

type FormValues = z.infer<typeof formSchema>;

const GRADE_WEIGHTS = {
  highSchool: 30,
  qudrat: 30,
  tahsili: 40
};

export function WeightedGradeCalculatorTool() {
  const [result, setResult] = useState<{ finalGrade: number } | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      highSchoolGrade: 0,
      qudratGrade: 0,
      tahsiliGrade: 0,
    },
  });

  const onSubmit = (data: FormValues) => {
    const weightedSum = 
      (data.highSchoolGrade * GRADE_WEIGHTS.highSchool / 100) +
      (data.qudratGrade * GRADE_WEIGHTS.qudrat / 100) +
      (data.tahsiliGrade * GRADE_WEIGHTS.tahsili / 100);
    
    setResult({ finalGrade: weightedSum });
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>حساب النسبة الموزونة</CardTitle>
        <CardDescription>احسب نسبتك الموزونة للقبول الجامعي بناءً على درجات الثانوية والقدرات والتحصيلي.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            
            {/* جدول الدرجات */}
            <div className="overflow-hidden rounded-lg border">
              {/* رأس الجدول */}
              <div className="grid grid-cols-3 bg-primary text-primary-foreground">
                <div className="p-4 text-center font-semibold border-r border-primary-foreground/20">الوصف</div>
                <div className="p-4 text-center font-semibold border-r border-primary-foreground/20">درجتك</div>
                <div className="p-4 text-center font-semibold">معدل الجامعة</div>
              </div>

              {/* صف درجة الثانوية */}
              <div className="grid grid-cols-3 bg-gray-50 border-b">
                <div className="p-4 text-center font-medium border-r">
                  درجة الثانوية
                </div>
                <div className="p-2 border-r">
                  <FormField
                    control={form.control}
                    name="highSchoolGrade"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            min="0"
                            max="100"
                            className="text-center"
                            placeholder="0"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="p-4 text-center bg-primary/10">
                  <span className="font-mono text-lg">% {GRADE_WEIGHTS.highSchool}</span>
                </div>
              </div>

              {/* صف درجة القدرات */}
              <div className="grid grid-cols-3 bg-white border-b">
                <div className="p-4 text-center font-medium border-r">
                  درجة القدرات
                </div>
                <div className="p-2 border-r">
                  <FormField
                    control={form.control}
                    name="qudratGrade"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            min="0"
                            max="100"
                            className="text-center"
                            placeholder="0"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="p-4 text-center bg-primary/10">
                  <span className="font-mono text-lg">% {GRADE_WEIGHTS.qudrat}</span>
                </div>
              </div>

              {/* صف درجة التحصيلي */}
              <div className="grid grid-cols-3 bg-gray-50">
                <div className="p-4 text-center font-medium border-r">
                  درجة التحصيلي
                </div>
                <div className="p-2 border-r">
                  <FormField
                    control={form.control}
                    name="tahsiliGrade"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            min="0"
                            max="100"
                            className="text-center"
                            placeholder="0"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="p-4 text-center bg-primary/10">
                  <span className="font-mono text-lg">% {GRADE_WEIGHTS.tahsili}</span>
                </div>
              </div>
            </div>

            {/* زر الحساب والنتيجة */}
            <div className="flex flex-col items-center space-y-4 pt-6">
              <Button type="submit" size="lg" className="bg-green-600 hover:bg-green-700 text-white px-8">
                احسب
              </Button>
              
              {result && (
                <div className="text-center p-6 bg-green-50 rounded-lg border-2 border-green-200">
                  <p className="text-sm text-green-600 mb-2">النسبة الموزونة</p>
                  <p className="text-4xl font-bold text-green-700 font-mono">
                    {result.finalGrade.toFixed(2)}%
                  </p>
                </div>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
