
'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Calendar, type CalendarProps } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import HijriDate from 'hijri-date';

// Helper to format a Gregorian date into a Hijri string
function formatToHijri(date: Date): string {
  if (!date) return 'اختر تاريخًا';
  return new Intl.DateTimeFormat('ar-SA-u-ca-islamic', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
  }).format(date);
}

export function DateConverterTool() {
  const [gregorianDate, setGregorianDate] = useState<Date | undefined>(new Date());
  const [hijriDate, setHijriDate] = useState<Date | undefined>(new Date());
  
  const handleGregorianSelect = (date: Date | undefined) => {
    if (date) {
      setGregorianDate(date);
      setHijriDate(date); // Sync the other calendar
    }
  };

  const handleHijriSelect = (date: Date | undefined) => {
    if (date) {
        // 'date' is the Gregorian equivalent of the selected Hijri date
        setHijriDate(date);
        setGregorianDate(date); // Sync the other calendar
    }
  };

  const formattedGregorian = gregorianDate
    ? format(gregorianDate, 'EEEE, d MMMM yyyy', { locale: ar })
    : 'اختر تاريخًا';
  
  const formattedHijri = hijriDate
    ? formatToHijri(hijriDate)
    : 'اختر تاريخًا';

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>محول التاريخ</CardTitle>
        <CardDescription>التحويل بين التاريخ الميلادي والهجري بسهولة ودقة.</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

          {/* Gregorian to Hijri */}
          <div className="flex flex-col items-center">
            <h3 className="text-lg font-semibold mb-2">من ميلادي إلى هجري</h3>
            <div className="p-4 bg-primary/10 rounded-lg w-full text-center mb-4">
              <p className="text-sm text-muted-foreground">التاريخ الهجري الموافق</p>
              <p className="text-xl font-bold font-mono text-primary">{formattedHijri}</p>
            </div>
            <Calendar
              mode="single"
              selected={gregorianDate}
              onSelect={handleGregorianSelect}
              className="rounded-md border"
              locale={ar}
              dir="rtl"
              captionLayout="dropdown-buttons"
              fromYear={new Date().getFullYear() - 100}
              toYear={new Date().getFullYear() + 10}
            />
          </div>

          {/* Hijri to Gregorian */}
          <div className="flex flex-col items-center">
            <h3 className="text-lg font-semibold mb-2">من هجري إلى ميلادي</h3>
            <div className="p-4 bg-primary/10 rounded-lg w-full text-center mb-4">
              <p className="text-sm text-muted-foreground">التاريخ الميلادي الموافق</p>
              <p className="text-xl font-bold font-mono text-primary">{formattedGregorian}</p>
            </div>
            <Calendar
              mode="single"
              calendarType="hijri"
              selected={hijriDate}
              onSelect={handleHijriSelect}
              className="rounded-md border"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
