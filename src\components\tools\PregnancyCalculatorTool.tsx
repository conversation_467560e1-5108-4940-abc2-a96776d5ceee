
'use client';

import { useState } from 'react';
import { useForm, Control } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Baby } from 'lucide-react';
import { format, addDays, differenceInWeeks, differenceInDays } from 'date-fns';
import { ar } from 'date-fns/locale';
import { Input } from '../ui/input';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  day: requiredNumber('اليوم مطلوب').int().min(1).max(31),
  month: requiredNumber('الشهر مطلوب').int().min(1).max(12),
  year: requiredNumber('السنة مطلوبة').int().min(new Date().getFullYear() - 2).max(new Date().getFullYear()),
}).refine(data => {
    try {
        const date = new Date(data.year, data.month - 1, data.day);
        return date.getFullYear() === data.year && date.getMonth() === data.month - 1 && date.getDate() === data.day && date <= new Date();
    } catch {
        return false;
    }
}, {
    message: 'تاريخ غير صالح أو في المستقبل.',
    path: ['day'],
});

type FormValues = z.infer<typeof FormSchema>;

interface Result {
  dueDate: Date;
  currentWeek: number;
  remainingDays: number;
}

const DateFields = ({ control }: { control: Control<FormValues> }) => (
    <div className="grid grid-cols-3 gap-4">
        <FormField
            control={control}
            name="day"
            render={({ field }) => (
                <FormItem>
                    <FormLabel>اليوم</FormLabel>
                    <FormControl>
                        <Input type="number" placeholder="اليوم" min="1" max="31" {...field} />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
        <FormField
            control={control}
            name="month"
            render={({ field }) => (
                <FormItem>
                    <FormLabel>الشهر</FormLabel>
                    <FormControl>
                        <Input type="number" placeholder="الشهر" min="1" max="12" {...field} />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
        <FormField
            control={control}
            name="year"
            render={({ field }) => (
                <FormItem>
                    <FormLabel>السنة</FormLabel>
                    <FormControl>
                        <Input type="number" placeholder="السنة" min="1" {...field} />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
    </div>
);


export function PregnancyCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
  });

  function onSubmit(data: FormValues) {
    const lmp = new Date(data.year, data.month - 1, data.day);
    const dueDate = addDays(lmp, 280);
    const currentWeek = differenceInWeeks(new Date(), lmp);
    const remainingDays = differenceInDays(dueDate, new Date());
    
    setResult({ dueDate, currentWeek, remainingDays });
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حاسبة الحمل والولادة</CardTitle>
        <CardDescription>أدخلي تاريخ أول يوم من آخر دورة شهرية لتوقّع موعد ولادتك.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormLabel>أول يوم من آخر دورة شهرية (LMP)</FormLabel>
            <DateFields control={form.control} />
            <Button type="submit" className="w-full">احسبي موعد الولادة</Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-4">النتائج التقديرية</h3>
             <div className="p-6 bg-primary/10 rounded-lg mb-4">
                <Baby className="w-12 h-12 text-primary mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">موعد الولادة المتوقع</p>
                <p className="text-3xl font-bold font-mono text-primary">
                  {format(result.dueDate, 'EEEE, d MMMM yyyy', { locale: ar })}
                </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-secondary rounded-lg">
                <p className="text-3xl font-bold font-mono">{result.currentWeek}</p>
                <p className="text-sm text-muted-foreground">أنتِ في الأسبوع</p>
              </div>
              <div className="p-4 bg-secondary rounded-lg">
                <p className="text-3xl font-bold font-mono">{result.remainingDays}</p>
                <p className="text-sm text-muted-foreground">يومًا متبقيًا (تقريبًا)</p>
              </div>
            </div>
            <p className="text-xs text-muted-foreground mt-4">
                هذه مجرد تقديرات. موعد الولادة الفعلي قد يختلف. استشيري طبيبكِ دائمًا.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
