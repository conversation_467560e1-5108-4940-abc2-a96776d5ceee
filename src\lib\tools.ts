
import type { LucideIcon } from 'lucide-react';
import type { ComponentType } from 'react';
import {
  Landmark,
  ArrowRightLeft,
  CalendarClock,
  HeartPulse,
  GraduationCap,
  Code,
  Sparkles,
  Puzzle,
  Wallet,
  // Tool icons
  ClipboardList,
  Receipt,
  Gem,
  TrendingUp,
  Coins,
  Percent,
  Box,
  Calculator,
  Timer,
  Clock,
  History,
  PiggyBank,
  QrCode,
  SpellCheck,
  Ruler,
  Repeat,
  Flame,
  Baby,
  CalendarHeart,
  Cake,
  CalendarRange,
  CalendarDays,
  BookOpen,
  Users,
  Globe,
  MessageCircle,
  SquareRadical,
  Divide,
  Replace,
  CalendarCheck,
  Moon,
  Sun,
  School,
  Coffee,
  Building,
  Weight,
  Type,
  EyeOff,
  Wand2,
  Beef,
  Route,
  Pen,
} from 'lucide-react';

// Import tool components
import { AgeCalculatorTool } from '@/components/tools/AgeCalculatorTool';
import { BmiCalculatorTool } from '@/components/tools/BmiCalculatorTool';
import { CurrencyConverterTool } from '@/components/tools/CurrencyConverterTool';
import { DiscountCalculatorTool } from '@/components/tools/DiscountCalculatorTool';
import { NumberToWordsTool } from '@/components/tools/NumberToWordsTool';
import { PercentageCalculatorTool } from '@/components/tools/PercentageCalculatorTool';
import { QrCodeGeneratorTool } from '@/components/tools/QrCodeGeneratorTool';
import { SimpleCalculatorTool } from '@/components/tools/SimpleCalculatorTool';
import { SummarizeArabicTextTool } from '@/components/tools/SummarizeArabicTextTool';
import { TextRepeaterTool } from '@/components/tools/TextRepeaterTool';
import { VatCalculatorTool } from '@/components/tools/VatCalculatorTool';
import { GoldPriceTool } from '@/components/tools/GoldPriceTool';
import { AramcoStockTool } from '@/components/tools/AramcoStockTool';
import { ZakatCalculatorTool } from '@/components/tools/ZakatCalculatorTool';
import { CbmCalculatorTool } from '@/components/tools/CbmCalculatorTool';
import { HourlyWageCalculatorTool } from '@/components/tools/HourlyWageCalculatorTool';
import { OvertimeCalculatorTool } from '@/components/tools/OvertimeCalculatorTool';
import { RetirementCalculatorTool } from '@/components/tools/RetirementCalculatorTool';
import { UnitConverterTool } from '@/components/tools/UnitConverterTool';
import { CaloriesCalculatorTool } from '@/components/tools/CaloriesCalculatorTool';
import { PregnancyCalculatorTool } from '@/components/tools/PregnancyCalculatorTool';
import { OvulationCalculatorTool } from '@/components/tools/OvulationCalculatorTool';
import { DateDifferenceTool } from '@/components/tools/DateDifferenceTool';
import { DateConverterTool } from '@/components/tools/DateConverterTool';
import { GpaCalculatorTool } from '@/components/tools/GpaCalculatorTool';
import { WeightedGradeCalculatorTool } from '@/components/tools/WeightedGradeCalculatorTool';
import { SampleSizeCalculatorTool } from '@/components/tools/SampleSizeCalculatorTool';
import { MyIpTool } from '@/components/tools/MyIpTool';
import { WhatsappToolsTool } from '@/components/tools/WhatsappToolsTool';
import { SqrtCalculatorTool } from '@/components/tools/SqrtCalculatorTool';
import { AverageCalculatorTool } from '@/components/tools/AverageCalculatorTool';
import { ReverseTextTool } from '@/components/tools/ReverseTextTool';
import { CountdownCardTool } from '@/components/tools/CountdownCardTool';
import { OunceToGramConverterTool } from '@/components/tools/OunceToGramConverterTool';
import { WordCountTool } from '@/components/tools/WordCountTool';
import { InvisibleCharacterTool } from '@/components/tools/InvisibleCharacterTool';
import { ParaphraseTextTool } from '@/components/tools/ParaphraseTextTool';
import { ProteinCalculatorTool } from '@/components/tools/ProteinCalculatorTool';
import { MileKilometerConverterTool } from '@/components/tools/MileKilometerConverterTool';
import { ArabicNameDecoratorTool } from '@/components/tools/ArabicNameDecoratorTool';
import { ZodiacSignCalculatorTool } from '@/components/tools/ZodiacSignCalculatorTool';


// Import data fetching functions
import { getCurrencyRates } from './actions/currency';
import { getIpInfo } from './actions/ip';
import { getGoldPrices } from './actions/gold';
import { getAramcoStock } from './actions/aramco';
import * as DateCalculators from './dates';

export interface Tool {
  name: string;
  slug: string;
  path: string;
  description: string;
  icon?: LucideIcon;
  component?: ComponentType<any>;
  getData?: () => Promise<any>;
  seoDescription?: string;
  faq?: { question: string; answer: string }[];
}

export interface ToolCategory {
  name: string;
  slug: string;
  description: string;
  icon: LucideIcon;
  tools: Tool[];
}

export const toolCategories: ToolCategory[] = [
  {
    name: 'أدوات الذكاء الاصطناعي',
    slug: 'ai-tools',
    description: 'استخدم قوة الذكاء الاصطناعي للمساعدة في مهامك.',
    icon: Sparkles,
    tools: [
      { 
        name: 'ملخص النصوص العربية', 
        slug: 'summarize-arabic-text', 
        path: '/tools/summarize-arabic-text', 
        description: 'تلخيص النصوص العربية الطويلة بضغطة زر.', 
        component: SummarizeArabicTextTool, 
        icon: ClipboardList,
      },
      { 
        name: 'أداة إعادة صياغة النص', 
        slug: 'paraphrase-text', 
        path: '/tools/paraphrase-text', 
        description: 'أعد صياغة النصوص والعبارات بأسلوب مختلف.', 
        component: ParaphraseTextTool, 
        icon: Wand2,
      },
    ],
  },
  {
    name: 'الحاسبات المالية',
    slug: 'financial-calculators',
    description: 'أدوات لإدارة أموالك وحساباتك بدقة وسهولة.',
    icon: Landmark,
    tools: [
      { 
        name: 'حساب ضريبة القيمة المضافة', 
        slug: 'vat-calculator', 
        path: '/tools/vat-calculator', 
        description: 'حساب ضريبة القيمة المضافة بسهولة.', 
        component: VatCalculatorTool, 
        icon: Receipt,
      },
      { 
        name: 'تحويل العملات', 
        slug: 'currency-converter', 
        path: '/tools/currency-converter', 
        description: 'أسعار صرف العملات محدثة باستمرار.', 
        component: CurrencyConverterTool, 
        getData: getCurrencyRates, 
        icon: ArrowRightLeft,
      },
      { 
        name: 'أسعار الذهب اليوم', 
        slug: 'gold-price', 
        path: '/tools/gold-price', 
        description: 'اطلع على أسعار الذهب لحظة بلحظة.', 
        component: GoldPriceTool,
        getData: () => getGoldPrices(), 
        icon: Gem,
      },
      { 
        name: 'اونصة الذهب كم جرام',
        slug: 'ounce-to-gram-converter',
        path: '/tools/ounce-to-gram-converter',
        description: 'تحويل أونصة تروي (وحدة قياس المعادن الثمينة) إلى جرام.',
        component: OunceToGramConverterTool,
        icon: Weight,
      },
      { 
        name: 'سعر سهم أرامكو اليوم', 
        slug: 'aramco-stock', 
        path: '/tools/aramco-stock', 
        description: 'تابع سعر سهم شركة أرامكو السعودية.', 
        component: AramcoStockTool,
        getData: getAramcoStock, 
        icon: TrendingUp,
      },
      { 
        name: 'حساب زكاة المال', 
        slug: 'zakat-calculator', 
        path: '/tools/zakat-calculator', 
        description: 'احسب قيمة الزكاة الواجبة على أموالك.', 
        component: ZakatCalculatorTool, 
        icon: Coins,
      },
      { 
        name: 'حساب الخصم', 
        slug: 'discount-calculator', 
        path: '/tools/discount-calculator', 
        description: 'معرفة السعر النهائي بعد الخصم.', 
        component: DiscountCalculatorTool, 
        icon: Percent,
      },
      { 
        name: 'حساب CBM للشحن', 
        slug: 'cbm-calculator', 
        path: '/tools/cbm-calculator', 
        description: 'حساب حجم الشحنات التجارية بالمتر المكعب.', 
        component: CbmCalculatorTool, 
        icon: Box,
      },
      { 
        name: 'حاسبة النسبة المئوية', 
        slug: 'percentage-calculator', 
        path: '/tools/percentage-calculator', 
        description: 'إجراء كافة حسابات النسبة المئوية.', 
        component: PercentageCalculatorTool, 
        icon: Calculator,
      },
    ],
  },
  {
    name: 'أدوات الرواتب والأجور',
    slug: 'payroll-tools',
    description: 'حسابات متعلقة بالراتب والتقاعد والأجور.',
    icon: Wallet,
    tools: [
        {
          name: 'كم باقي على الراتب',
          slug: 'saudi-salary-countdown',
          path: '/tools/saudi-salary-countdown',
          description: 'عداد تنازلي لموعد صرف رواتب الموظفين في السعودية (يوم 27).',
          component: CountdownCardTool,
          getData: async () => Promise.resolve(DateCalculators.getSalaryCountdown()),
          icon: Wallet,
        },
        { 
          name: 'حساب أجر الساعة', 
          slug: 'hourly-wage-calculator', 
          path: '/tools/hourly-wage-calculator', 
          description: 'احسب قيمة أجرك بالساعة.', 
          component: HourlyWageCalculatorTool, 
          icon: Clock,
        },
        { 
          name: 'حساب الأجر الإضافي', 
          slug: 'overtime-calculator', 
          path: '/tools/overtime-calculator', 
          description: 'معرفة قيمة العمل الإضافي.', 
          component: OvertimeCalculatorTool, 
          icon: History,
        },
        { 
          name: 'حاسبة التقاعد', 
          slug: 'retirement-calculator', 
          path: '/tools/retirement-calculator', 
          description: 'خطط لمستقبلك المالي بعد التقاعد.', 
          component: RetirementCalculatorTool, 
          icon: PiggyBank,
        },
        {
          name: 'عداد حساب المواطن',
          slug: 'citizen-account-countdown',
          path: '/tools/citizen-account-countdown',
          description: 'الوقت المتبقي حتى إيداع دفعة حساب المواطن القادمة.',
          component: CountdownCardTool,
          getData: async () => Promise.resolve(DateCalculators.getCitizenAccount()),
          icon: Users,
        },
        {
          name: 'عداد راتب التقاعد',
          slug: 'retirement-pension-countdown',
          path: '/tools/retirement-pension-countdown',
          description: 'الوقت المتبقي حتى إيداع رواتب المتقاعدين.',
          component: CountdownCardTool,
          getData: async () => Promise.resolve(DateCalculators.getRetirementPension()),
          icon: Building,
        },
        {
          name: 'عداد الدعم السكني',
          slug: 'housing-support-countdown',
          path: '/tools/housing-support-countdown',
          description: 'الوقت المتبقي حتى إيداع الدعم السكني للمستفيدين.',
          component: CountdownCardTool,
          getData: async () => Promise.resolve(DateCalculators.getHousingSupport()),
          icon: Landmark,
        },
    ]
  },
  {
    name: 'المحولات',
    slug: 'converters',
    description: 'تحويل بين الوحدات والصيغ المختلفة بسهولة.',
    icon: ArrowRightLeft,
    tools: [
      { 
        name: 'مولد رمز QR', 
        slug: 'qr-code-generator', 
        path: '/tools/qr-code-generator', 
        description: 'إنشاء رموز QR Code لأي نص أو رابط.', 
        component: QrCodeGeneratorTool, 
        icon: QrCode,
      },
      { 
        name: 'تفقيط الأرقام', 
        slug: 'number-to-words', 
        path: '/tools/number-to-words', 
        description: 'تحويل الأرقام إلى كلمات عربية.', 
        component: NumberToWordsTool, 
        icon: SpellCheck,
      },
      { 
        name: 'تحويل الوحدات', 
        slug: 'unit-converter', 
        path: '/tools/unit-converter', 
        description: 'تحويل الطول والوزن والحرارة وغيرها.', 
        component: UnitConverterTool, 
        icon: Ruler,
      },
      {
        name: 'تحويل من ميل إلى كيلو',
        slug: 'mile-kilometer-converter',
        path: '/tools/mile-kilometer-converter',
        description: 'تحويل المسافات بين الميل والكيلومتر بسهولة.',
        component: MileKilometerConverterTool,
        icon: Route,
      },
      { 
        name: 'تكرار النص', 
        slug: 'text-repeater', 
        path: '/tools/text-repeater', 
        description: 'تكرار أي نص أو كلمة لعدد معين من المرات.', 
        component: TextRepeaterTool, 
        icon: Repeat,
      },
      { 
        name: 'عكس النص', 
        slug: 'reverse-text', 
        path: '/tools/reverse-text', 
        description: 'للنصوص العربية في البرامج غير الداعمة.', 
        component: ReverseTextTool, 
        icon: Replace,
      },
       { 
        name: 'عداد الكلمات', 
        slug: 'word-count', 
        path: '/tools/word-count', 
        description: 'حساب عدد الكلمات والأحرف في نص معين.', 
        component: WordCountTool, 
        icon: Type,
      },
      { 
        name: 'مولد الحرف المخفي (نص فارغ)', 
        slug: 'invisible-character', 
        path: '/tools/invisible-character', 
        description: 'نسخ حروف غير مرئية لاستخدامها في التطبيقات.', 
        component: InvisibleCharacterTool, 
        icon: EyeOff,
      },
    ],
  },
  {
    name: 'أدوات الصحة والحياة اليومية',
    slug: 'health-daily-life-tools',
    description: 'أدوات لمتابعة صحتك وتنظيم أمور حياتك.',
    icon: HeartPulse,
    tools: [
      { 
        name: 'حاسبة مؤشر كتلة الجسم', 
        slug: 'bmi-calculator', 
        path: '/tools/bmi-calculator', 
        description: 'معرفة ما إذا كان وزنك صحيًا.', 
        component: BmiCalculatorTool, 
        icon: HeartPulse,
      },
      { 
        name: 'حاسبة السعرات الحرارية', 
        slug: 'calories-calculator', 
        path: '/tools/calories-calculator', 
        description: 'حساب احتياجك اليومي من السعرات.', 
        component: CaloriesCalculatorTool, 
        icon: Flame,
      },
      { 
        name: 'حاسبة الحمل والولادة', 
        slug: 'pregnancy-calculator', 
        path: '/tools/pregnancy-calculator', 
        description: 'توقعي موعد ولادتك وتابعي حملك.', 
        component: PregnancyCalculatorTool, 
        icon: Baby,
      },
      { 
        name: 'حاسبة التبويض', 
        slug: 'ovulation-calculator', 
        path: '/tools/ovulation-calculator', 
        description: 'معرفة أيام التبويض لزيادة فرص الحمل.', 
        component: OvulationCalculatorTool, 
        icon: CalendarHeart,
      },
      { 
        name: 'حاسبة البروتين', 
        slug: 'protein-calculator', 
        path: '/tools/protein-calculator', 
        description: 'احسب احتياجك اليومي من البروتين.', 
        component: ProteinCalculatorTool, 
        icon: Beef,
      },
    ],
  },
  {
    name: 'التواريخ والأوقات',
    slug: 'dates-times',
    description: 'أدوات متنوعة للتعامل مع التواريخ والأوقات.',
    icon: CalendarClock,
    tools: [
      {
        name: 'عداد عيد الفطر',
        slug: 'eid-alfitr-countdown',
        path: '/tools/eid-alfitr-countdown',
        description: 'الوقت المتبقي حتى أول أيام عيد الفطر السعيد.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getEidAlFitr()),
        icon: Moon,
      },
      {
        name: 'عداد عيد الأضحى',
        slug: 'eid-aladha-countdown',
        path: '/tools/eid-aladha-countdown',
        description: 'الوقت المتبقي حتى أول أيام عيد الأضحى المبارك.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getEidAlAdha()),
        icon: Moon,
      },
      {
        name: 'عداد يوم عرفة',
        slug: 'arafah-day-countdown',
        path: '/tools/arafah-day-countdown',
        description: 'الوقت المتبقي حتى يوم عرفة، ركن الحج الأعظم.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getArafahDay()),
        icon: Sun,
      },
      {
        name: 'عداد يوم التأسيس',
        slug: 'founding-day-countdown',
        path: '/tools/founding-day-countdown',
        description: 'الوقت المتبقي حتى الاحتفال بيوم التأسيس للمملكة.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getFoundingDay()),
        icon: Landmark,
      },
      {
        name: 'عداد اليوم الوطني السعودي',
        slug: 'saudi-national-day-countdown',
        path: '/tools/saudi-national-day-countdown',
        description: 'الوقت المتبقي حتى الاحتفال باليوم الوطني للمملكة.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getNationalDay()),
        icon: CalendarCheck,
      },
      {
        name: 'عداد الإجازة القادمة',
        slug: 'next-vacation-countdown',
        path: '/tools/next-vacation-countdown',
        description: 'الوقت المتبقي حتى أقرب إجازة دراسية قادمة.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getNextVacation()),
        icon: Coffee,
      },
       {
        name: 'كم باقي على المدرسة السعودية​',
        slug: 'study-calendar-countdown',
        path: '/tools/study-calendar-countdown',
        description: 'مواعيد بداية العام الدراسي والإجازات بناءً على التقويم الرسمي المعتمد.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getSaudiCalendar()),
        icon: School,
      },
      {
        name: 'عداد بداية الشتاء',
        slug: 'winter-countdown',
        path: '/tools/winter-countdown',
        description: 'الوقت المتبقي حتى بداية فصل الشتاء فلكيًا.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getWinterStart()),
        icon: Timer,
      },
       {
        name: 'عداد نهاية الصيف',
        slug: 'summer-end-countdown',
        path: '/tools/summer-end-countdown',
        description: 'الوقت المتبقي حتى نهاية فصل الصيف فلكيًا.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getSummerEnd()),
        icon: Sun,
      },
      { 
        name: 'حاسبة العمر', 
        slug: 'age-calculator', 
        path: '/tools/age-calculator', 
        description: 'احسب عمرك بالسنوات والشهور والأيام.', 
        component: AgeCalculatorTool, 
        icon: Cake,
      },
      { 
        name: 'حساب الفرق بين تاريخين', 
        slug: 'date-difference', 
        path: '/tools/date-difference', 
        description: 'حساب المدة بين تاريخين مختلفين.', 
        component: DateDifferenceTool, 
        icon: CalendarRange,
      },
      { 
        name: 'محول التاريخ', 
        slug: 'date-converter', 
        path: '/tools/date-converter', 
        description: 'التحويل بين التاريخ الميلادي والهجري والعكس.', 
        component: DateConverterTool, 
        icon: CalendarDays,
      },
    ],
  },
  {
    name: 'أدوات التعليم',
    slug: 'education-tools',
    description: 'أدوات مفيدة للطلاب والباحثين في مسيرتهم الدراسية.',
    icon: GraduationCap,
    tools: [
      { 
        name: 'حساب المعدل التراكمي', 
        slug: 'gpa-calculator', 
        path: '/tools/gpa-calculator', 
        description: 'احسب معدلك التراكمي بسهولة.', 
        component: GpaCalculatorTool, 
        icon: GraduationCap,
      },
      { 
        name: 'حساب النسبة الموزونة', 
        slug: 'weighted-grade-calculator', 
        path: '/tools/weighted-grade-calculator', 
        description: 'حساب الدرجة النهائية الموزونة.', 
        component: WeightedGradeCalculatorTool, 
        icon: BookOpen,
      },
      { 
        name: 'تحديد حجم العينة', 
        slug: 'sample-size-calculator', 
        path: '/tools/sample-size-calculator', 
        description: 'تحديد حجم العينة المناسب لبحثك العلمي.', 
        component: SampleSizeCalculatorTool, 
        icon: Users,
      },
    ],
  },
  {
    name: 'أدوات الويب والمطورين',
    slug: 'web-developer-tools',
    description: 'مجموعة أدوات عملية للمطورين وأصحاب المواقع.',
    icon: Code,
    tools: [
      { 
        name: 'معرفة IP الخاص بي', 
        slug: 'my-ip', 
        path: '/tools/my-ip', 
        description: 'عرض عنوان IP العام الخاص بك.', 
        component: MyIpTool, 
        getData: getIpInfo, 
        icon: Globe,
      },
      { 
        name: 'انشاء رابط WhatsApp', 
        slug: 'whatsapp-tools', 
        path: '/tools/whatsapp-tools', 
        description: 'حوّل رقم الواتس إلى رابط مباشر، أرسل رسائل بدون حفظ الرقم، واصنع رابط واتس اب بسهولة.', 
        component: WhatsappToolsTool,
        getData: getIpInfo, 
        icon: MessageCircle,
      },
    ],
  },
  {
    name: 'أدوات متنوعة',
    slug: 'miscellaneous-tools',
    description: 'مجموعة من الأدوات المتنوعة والمفيدة.',
    icon: Puzzle,
    tools: [
      { 
        name: 'آلة حاسبة بسيطة', 
        slug: 'simple-calculator', 
        path: '/tools/simple-calculator', 
        description: 'إجراء العمليات الحسابية الأساسية.', 
        component: SimpleCalculatorTool, 
        icon: Calculator,
      },
      { 
        name: 'حساب الجذر التربيعي', 
        slug: 'sqrt-calculator', 
        path: '/tools/sqrt-calculator', 
        description: 'إيجاد الجذر التربيعي لأي رقم.', 
        component: SqrtCalculatorTool, 
        icon: SquareRadical,
      },
      { 
        name: 'حساب الوسط الحسابي', 
        slug: 'average-calculator', 
        path: '/tools/average-calculator', 
        description: 'حساب المتوسط الحسابي لمجموعة أرقام.', 
        component: AverageCalculatorTool, 
        icon: Divide,
      },
      { 
        name: 'زخرفة اسماء عربية', 
        slug: 'arabic-name-decorator', 
        path: '/tools/arabic-name-decorator', 
        description: 'زخرفة اسمك باللغة العربية بأنماط فريدة.', 
        component: ArabicNameDecoratorTool, 
        icon: Pen,
      },
       { 
        name: 'حاسبة الأبراج', 
        slug: 'zodiac-sign-calculator', 
        path: '/tools/zodiac-sign-calculator', 
        description: 'اكتشف برجك الشمسي بناءً على تاريخ ميلادك.', 
        component: ZodiacSignCalculatorTool, 
        icon: Sparkles,
      },
    ],
  },
];
