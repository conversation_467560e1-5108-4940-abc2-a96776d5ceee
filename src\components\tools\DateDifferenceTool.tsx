
'use client';

import { useState } from 'react';
import { useForm, Control } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { intervalToDuration, differenceInDays, differenceInMonths } from 'date-fns';
import { Input } from '../ui/input';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const dateSchema = z.object({
  day: requiredNumber('اليوم مطلوب').int().min(1).max(31),
  month: requiredNumber('الشهر مطلوب').int().min(1).max(12),
  year: requiredNumber('السنة مطلوبة').int().min(1, "السنة يجب أن تكون موجبة.").max(new Date().getFullYear() + 100),
});

const FormSchema = z.object({
  startDate: dateSchema,
  endDate: dateSchema,
}).refine(data => {
  try {
    const start = new Date(data.startDate.year, data.startDate.month - 1, data.startDate.day);
    const end = new Date(data.endDate.year, data.endDate.month - 1, data.endDate.day);
    return end >= start;
  } catch {
    return false;
  }
}, {
  message: 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية.',
  path: ['endDate', 'day'],
});

type FormValues = z.infer<typeof FormSchema>;

interface Result {
  years: number;
  months: number;
  days: number;
  totalDays: number;
  totalMonths: number;
}

const DateInputGroup = ({ name, label, control }: { name: 'startDate' | 'endDate', label: string, control: Control<FormValues> }) => (
    <div className="space-y-2">
      <FormLabel>{label}</FormLabel>
      <div className="grid grid-cols-3 gap-2">
        <FormField name={`${name}.day`} control={control} render={({ field }) => (
          <FormItem><FormControl><Input type="number" placeholder="يوم" min="1" max="31" {...field} /></FormControl><FormMessage /></FormItem>
        )}/>
        <FormField name={`${name}.month`} control={control} render={({ field }) => (
          <FormItem><FormControl><Input type="number" placeholder="شهر" min="1" max="12" {...field} /></FormControl><FormMessage /></FormItem>
        )}/>
        <FormField name={`${name}.year`} control={control} render={({ field }) => (
          <FormItem><FormControl><Input type="number" placeholder="سنة" min="1" {...field} /></FormControl><FormMessage /></FormItem>
        )}/>
      </div>
    </div>
  );

export function DateDifferenceTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
  });

  function onSubmit(data: FormValues) {
    const startDate = new Date(data.startDate.year, data.startDate.month - 1, data.startDate.day);
    const endDate = new Date(data.endDate.year, data.endDate.month - 1, data.endDate.day);
    
    const duration = intervalToDuration({ start: startDate, end: endDate });
    const totalDays = differenceInDays(endDate, startDate);
    const totalMonths = differenceInMonths(endDate, startDate);

    setResult({
      years: duration.years || 0,
      months: duration.months || 0,
      days: duration.days || 0,
      totalDays,
      totalMonths
    });
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حساب الفرق بين تاريخين</CardTitle>
        <CardDescription>احسب المدة الزمنية بين أي تاريخين بالسنوات والأشهر والأيام.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <DateInputGroup name="startDate" label="تاريخ البداية" control={form.control} />
              <DateInputGroup name="endDate" label="تاريخ النهاية" control={form.control} />
            </div>
            <Button type="submit" className="w-full">احسب الفرق</Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-4">الفرق هو</h3>
            <div className="p-4 bg-primary/10 rounded-lg mb-4">
              <p className="text-2xl md:text-3xl font-bold text-primary">
                {result.years > 0 ? `${result.years} سنوات` : ''}
                {result.years > 0 && (result.months > 0 || result.days > 0) ? ', ' : ''}
                {result.months > 0 ? `${result.months} أشهر` : ''}
                {result.months > 0 && result.days > 0 ? ', ' : ''}
                {result.days > 0 ? `${result.days} أيام` : ''}
                {result.years === 0 && result.months === 0 && result.days === 0 ? 'لا يوجد فرق' : ''}
              </p>
              <p className="text-sm text-muted-foreground">المدة بالتفصيل</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-secondary rounded-lg">
                <p className="text-3xl font-bold font-mono">{result.totalMonths.toLocaleString()}</p>
                <p className="text-sm text-muted-foreground">إجمالي الأشهر</p>
              </div>
              <div className="p-4 bg-secondary rounded-lg">
                <p className="text-3xl font-bold font-mono">{result.totalDays.toLocaleString()}</p>
                <p className="text-sm text-muted-foreground">إجمالي الأيام</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
