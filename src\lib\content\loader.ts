
import { getDynamicSeoContent } from './dynamic-content';

export async function loadToolContent(slug: string) {
  try {
    // أولاً، تحقق من وجود محتوى ديناميكي
    const dynamicContent = getDynamicSeoContent(slug);
    if (dynamicContent) {
      return dynamicContent;
    }

    switch (slug) {
      case 'summarize-arabic-text':
        return (await import('./tools/summarize-arabic-text')).default;
      case 'paraphrase-text':
        return (await import('./tools/paraphrase-text')).default;
      case 'vat-calculator':
        return (await import('./tools/vat-calculator')).default;
      case 'currency-converter':
        return (await import('./tools/currency-converter')).default;
      case 'gold-price':
        return (await import('./tools/gold-price')).default;
      case 'aramco-stock':
        return (await import('./tools/aramco-stock')).default;
      case 'zakat-calculator':
        return (await import('./tools/zakat-calculator')).default;
      case 'discount-calculator':
        return (await import('./tools/discount-calculator')).default;
      case 'cbm-calculator':
        return (await import('./tools/cbm-calculator')).default;
      case 'percentage-calculator':
        return (await import('./tools/percentage-calculator')).default;
      case 'hourly-wage-calculator':
        return (await import('./tools/hourly-wage-calculator')).default;
      case 'overtime-calculator':
        return (await import('./tools/overtime-calculator')).default;
      case 'retirement-calculator':
        return (await import('./tools/retirement-calculator')).default;
      case 'qr-code-generator':
        return (await import('./tools/qr-code-generator')).default;
      case 'number-to-words':
        return (await import('./tools/number-to-words')).default;
      case 'unit-converter':
        return (await import('./tools/unit-converter')).default;
      case 'text-repeater':
        return (await import('./tools/text-repeater')).default;
      case 'bmi-calculator':
        return (await import('./tools/bmi-calculator')).default;
      case 'calories-calculator':
        return (await import('./tools/calories-calculator')).default;
      case 'protein-calculator':
        return (await import('./tools/protein-calculator')).default;
      case 'pregnancy-calculator':
        return (await import('./tools/pregnancy-calculator')).default;
      case 'ovulation-calculator':
        return (await import('./tools/ovulation-calculator')).default;
      case 'age-calculator':
        return (await import('./tools/age-calculator')).default;
      case 'date-difference':
        return (await import('./tools/date-difference')).default;
      case 'date-converter':
        return (await import('./tools/date-converter')).default;
      case 'gpa-calculator':
        return (await import('./tools/gpa-calculator')).default;
      case 'weighted-grade-calculator':
        return (await import('./tools/weighted-grade-calculator')).default;
      case 'sample-size-calculator':
        return (await import('./tools/sample-size-calculator')).default;
      case 'my-ip':
        return (await import('./tools/my-ip')).default;
      case 'whatsapp-tools':
        return (await import('./tools/whatsapp-tools')).default;
      case 'simple-calculator':
        return (await import('./tools/simple-calculator')).default;
      case 'sqrt-calculator':
        return (await import('./tools/sqrt-calculator')).default;
      case 'average-calculator':
        return (await import('./tools/average-calculator')).default;
      case 'reverse-text':
        return (await import('./tools/reverse-text')).default;
      case 'ounce-to-gram-converter':
        return (await import('./tools/ounce-to-gram-converter')).default;
      case 'word-count':
        return (await import('./tools/word-count')).default;
      case 'invisible-character':
        return (await import('./tools/invisible-character')).default;
      case 'mile-kilometer-converter':
        return (await import('./tools/mile-kilometer-converter')).default;
      case 'zodiac-sign-calculator':
        return (await import('./tools/zodiac-sign-calculator')).default;
      // Countdown tools now load their own content
      case 'saudi-salary-countdown':
        return (await import('./tools/salary-countdown')).default;
      case 'eid-alfitr-countdown':
        return (await import('./tools/eid-alfitr-countdown')).default;
      case 'eid-aladha-countdown':
        return (await import('./tools/eid-aladha-countdown')).default;
      case 'arafah-day-countdown':
        return (await import('./tools/arafah-day-countdown')).default;
      case 'founding-day-countdown':
        return (await import('./tools/founding-day-countdown')).default;
      case 'saudi-national-day-countdown':
        return (await import('./tools/saudi-national-day-countdown')).default;
      case 'citizen-account-countdown':
        return (await import('./tools/citizen-account-countdown')).default;
      case 'retirement-pension-countdown':
        return (await import('./tools/retirement-pension-countdown')).default;
      case 'housing-support-countdown':
        return (await import('./tools/housing-support-countdown')).default;
      case 'next-vacation-countdown':
        return (await import('./tools/next-vacation-countdown')).default;
      case 'winter-countdown':
        return (await import('./tools/winter-countdown')).default;
      case 'summer-end-countdown':
        return (await import('./tools/summer-end-countdown')).default;
      case 'study-calendar-countdown':
        return (await import('./tools/study-calendar-countdown')).default;
      default:
        // Generic fallback if a specific content file is not found
        try {
          return (await import(`./tools/${slug}`)).default;
        } catch (e) {
            return { seoDescription: null, faq: null };
        }
    }
  } catch (error) {
    console.warn(`Could not load content for tool slug: "${slug}"`, error);
    return { seoDescription: null, faq: null };
  }
}
